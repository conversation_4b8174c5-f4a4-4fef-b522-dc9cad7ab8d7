"""
GPS Data Processing Module
Extracts speed and timeline data from Navirec API responses for composite chart visualization
"""
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


def process_navirec_timeline_data(api_response: Dict[str, Any]) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]], str, str]:
    """
    Extract speed data and activity timeline from Navirec timeline API response.
    
    Args:
        api_response: The JSON response from Navirec vehicle_timeline endpoint
        
    Returns:
        Tuple of (speed_data, activity_timeline, start_time, end_time) from API response
    """
    speed_data = []
    activity_timeline = []
    api_start_time = ""
    api_end_time = ""
    
    try:
        # Extract start_time and end_time from API response
        if 'data' in api_response and api_response['data']:
            first_entry = api_response['data'][0]
            api_start_time = first_entry.get('start_time', '')
            api_end_time = first_entry.get('end_time', '')
        
        # Handle both single timeline entry and list of entries
        timeline_events = []
        if 'timeline_events' in api_response:
            # Direct timeline_events in response
            timeline_events = api_response['timeline_events']
        elif 'data' in api_response:
            # Data array containing timeline_events
            data = api_response['data']
            for entry in data:
                if 'timeline_events' in entry:
                    timeline_events.extend(entry['timeline_events'])
        
        if not timeline_events:
            return speed_data, activity_timeline, api_start_time, api_end_time
            
        # Process timeline events
        for event in timeline_events:
            # Only process trip events
            if event.get('event_type') == 'trip':
                trip = event['trip']
                activity = trip.get('activity', '')
                start_time = trip.get('start_time')
                end_time = trip.get('end_time')
                
                # Skip events with missing time data
                if not start_time or not end_time:
                    continue
                
                # Add to activity timeline
                activity_timeline.append({
                    'activity': activity,
                    'start_time': start_time,
                    'end_time': end_time,
                    'message': event.get('message', ''),
                    'address': event.get('address', ''),
                    'start_address': trip.get('start_address', event.get('address', '')),
                    'end_address': trip.get('end_address', event.get('address', '')),
                    'distance': float(trip.get('distance') or 0),  # Handle None values
                    'duration_minutes': calculate_duration_minutes(start_time, end_time)
                })
                
                # Add speed data points with gradual transitions
                if activity in ['driving', 'towing']:
                    max_speed = trip.get('max_speed') or 0
                    if max_speed and max_speed > 0:
                        # Create gradual speed curve for driving/towing segments
                        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
                        duration_seconds = (end_dt - start_dt).total_seconds()

                        # Towing typically has lower speeds than regular driving
                        speed_multiplier = 0.7 if activity == 'towing' else 1.0

                        # Create multiple points for smooth acceleration/deceleration
                        if duration_seconds > 60:  # Only for trips longer than 1 minute
                            # Acceleration phase (first 20% of trip)
                            accel_time = start_dt + (end_dt - start_dt) * 0.2
                            # Cruise phase (middle 60% of trip)
                            cruise_start = start_dt + (end_dt - start_dt) * 0.2
                            cruise_end = start_dt + (end_dt - start_dt) * 0.8
                            # Deceleration phase (last 20% of trip)
                            decel_time = start_dt + (end_dt - start_dt) * 0.8

                            # Add gradual speed points
                            speed_data.extend([
                                {
                                    'time': start_time,
                                    'speed': 0,
                                    'activity': activity,
                                    'start_address': trip.get('start_address', event.get('address', ''))
                                },
                                {
                                    'time': accel_time.isoformat(),
                                    'speed': float(max_speed) * speed_multiplier * 0.7,  # 70% of max speed during acceleration
                                    'activity': activity,
                                    'start_address': trip.get('start_address', event.get('address', ''))
                                },
                                {
                                    'time': cruise_start.isoformat(),
                                    'speed': float(max_speed) * speed_multiplier,  # Full speed during cruise
                                    'activity': activity,
                                    'start_address': trip.get('start_address', event.get('address', ''))
                                },
                                {
                                    'time': cruise_end.isoformat(),
                                    'speed': float(max_speed) * speed_multiplier,  # Maintain full speed
                                    'activity': activity,
                                    'start_address': trip.get('start_address', event.get('address', ''))
                                },
                                {
                                    'time': decel_time.isoformat(),
                                    'speed': float(max_speed) * speed_multiplier * 0.4,  # 40% of max speed during deceleration
                                    'activity': activity,
                                    'start_address': trip.get('start_address', event.get('address', ''))
                                },
                                {
                                    'time': end_time,
                                    'speed': 0,
                                    'activity': activity,
                                    'start_address': trip.get('start_address', event.get('address', ''))
                                }
                            ])
                        else:
                            # For short trips, use simpler curve
                            mid_time = start_dt + (end_dt - start_dt) * 0.5
                            speed_data.extend([
                                {
                                    'time': start_time,
                                    'speed': 0,
                                    'activity': activity,
                                    'start_address': trip.get('start_address', event.get('address', ''))
                                },
                                {
                                    'time': mid_time.isoformat(),
                                    'speed': float(max_speed) * speed_multiplier * 0.8,  # 80% of max speed at midpoint
                                    'activity': activity,
                                    'start_address': trip.get('start_address', event.get('address', ''))
                                },
                                {
                                    'time': end_time,
                                    'speed': 0,
                                    'activity': activity,
                                    'start_address': trip.get('start_address', event.get('address', ''))
                                }
                            ])
                elif activity == 'idling':
                    # Idling may have some low speed movement
                    max_speed = trip.get('max_speed') or 0
                    idle_speed = min(float(max_speed) if max_speed else 0, 15)  # Cap at 15 km/h for idling
                    speed_data.extend([
                        {
                            'time': start_time,
                            'speed': idle_speed,
                            'activity': activity,
                            'start_address': trip.get('start_address', event.get('address', ''))
                        },
                        {
                            'time': end_time,
                            'speed': idle_speed,
                            'activity': activity,
                            'start_address': trip.get('start_address', event.get('address', ''))
                        }
                    ])
                else:
                    # Add zero speed points for parking and offline activities
                    speed_data.extend([
                        {
                            'time': start_time,
                            'speed': 0,
                            'activity': activity,
                            'start_address': trip.get('start_address', event.get('address', ''))
                        },
                        {
                            'time': end_time,
                            'speed': 0,
                            'activity': activity,
                            'start_address': trip.get('start_address', event.get('address', ''))
                        }
                    ])
        
        # Sort by time (handle None values)
        speed_data.sort(key=lambda x: x['time'] or '9999-12-31T23:59:59Z')
        activity_timeline.sort(key=lambda x: x['start_time'] or '9999-12-31T23:59:59Z')
        
    except Exception as e:
        logger.error(f"Error processing Navirec timeline data: {str(e)}")
        
    return speed_data, activity_timeline, api_start_time, api_end_time


def get_activity_color(activity: str) -> str:
    """Return color for activity type matching Navirec platform styling."""
    activity_colors = {
        'driving': '#22c55e',      # Green
        'idling': '#f97316',       # Orange
        'parking': '#3b82f6',      # Blue
        'offline': '#6b7280',      # Gray
        'towing': '#8b5cf6',       # Purple
    }
    return activity_colors.get(activity, '#6b7280')  # Default to gray


def calculate_duration_minutes(start_time: str, end_time: str) -> float:
    """Calculate duration in minutes between two ISO timestamps."""
    try:
        if not start_time or not end_time:
            return 0.0
            
        start = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        end = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        duration = (end - start).total_seconds() / 60
        return round(duration, 1)
    except Exception as e:
        logger.error(f"Error calculating duration: {str(e)}")
        return 0.0


def format_address(address: str) -> str:
    """Simplify address for display."""
    if not address:
        return "Unknown Location"
    
    # Take first part before comma for brevity
    parts = address.split(',')
    return parts[0] if parts else address[:50] 